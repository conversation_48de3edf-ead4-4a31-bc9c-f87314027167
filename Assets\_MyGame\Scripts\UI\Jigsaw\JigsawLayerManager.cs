using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using FairyGUI;

/// <summary>
/// 底层piece或组的信息
/// </summary>
public class UnderlyingItem
{
    public bool IsGroup { get; set; }
    public JigsawPiece Piece { get; set; }
    public JigsawGroup Group { get; set; }

    public UnderlyingItem(JigsawPiece piece)
    {
        IsGroup = false;
        Piece = piece;
    }

    public UnderlyingItem(JigsawGroup group)
    {
        IsGroup = true;
        Group = group;
    }
}

/// <summary>
/// 智能层级管理器，负责管理所有拼块和thickness的层级
/// </summary>
public class JigsawLayerManager
{
    private GComponent rootContainer;
    private JigsawPanel parentPanel;
    private List<JigsawPiece> pieces = new List<JigsawPiece>();
    private Dictionary<JigsawPiece, GComponent> pieceThicknessMap = new Dictionary<JigsawPiece, GComponent>();
    public JigsawLayerManager(GComponent container, JigsawPanel panel)
    {
        rootContainer = container;
        parentPanel = panel;
    }

    /// <summary>
    /// 确保拼块有thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    private void EnsurePieceHasThickness(JigsawPiece piece)
    {
        if (piece == null) return;
        
        // 检查是否已经有thickness
        var existingThickness = GetThicknessFor(piece);
        if (existingThickness != null) return;
        
        // 创建新的thickness
        try
        {
            if (rootContainer == null)
            {
                UnityEngine.Debug.LogError("SmartLayerManager: rootContainer is null!");
                return;
            }
            
            var thicknessClone = FairyGUI.UIPackage.CreateObject("Jigsaw", "JigsawThickness").asCom;
            var thickness = thicknessClone.GetChild("thickness").asLoader;
            
            var url = $"ui://Z_Image_{piece.imageIndex}/piece_{piece.pieceIndex}";
            thickness.url = url;

#if UNITY_EDITOR
            thicknessClone.gameObjectName = "JigsawThickness" + piece.pieceIndex;
#endif
            // 将thickness添加到对应的容器（默认为普通piece容器）
            var pieceContainer = parentPanel.GetPieceContainer();
            pieceContainer.AddChild(thicknessClone);
            
            // 隐藏拼块自身的thickness
            piece.SetThicknessVisible(false);
            
            // 建立映射关系
            StorePieceThicknessMapping(piece, thicknessClone);
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"Failed to create thickness for piece {piece.pieceIndex}: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 存储拼块thickness映射
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    private void StorePieceThicknessMapping(JigsawPiece piece, GComponent thickness)
    {
        pieceThicknessMap[piece] = thickness;
    }
    
    /// <summary>
    /// 移除拼块的thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    private void RemovePieceThickness(JigsawPiece piece)
    {
        if (piece == null) return;
        
        if (pieceThicknessMap.TryGetValue(piece, out GComponent thickness))
        {
            // 从当前容器中移除,todo 复用thickness
            if (thickness.parent != null)
            {
                thickness.parent.RemoveChild(thickness, true);
            }

            // 移除映射关系
            pieceThicknessMap.Remove(piece);

            // 显示拼块自身的thickness
            piece.SetThicknessVisible(true);
        }
    }
    
    /// <summary>
    /// 更新thickness的位置坐标
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="thickness">thickness组件</param>
    private void UpdateThicknessPosition(JigsawPiece piece, GComponent thickness)
    {
        if (piece == null || thickness == null || piece.isDisposed) return;

        try
        {
            // 获取拼块的全局位置
            Vector2 pieceGlobalPos = piece.LocalToGlobal(Vector2.zero);

            // 转换为thickness父容器的本地坐标
            if (thickness.parent != null)
            {
                Vector2 thicknessLocalPos = thickness.parent.GlobalToLocal(pieceGlobalPos);
                thickness.SetXY(thicknessLocalPos.x, thicknessLocalPos.y);
            }
        }
        catch (System.Exception)
        {
            // 忽略坐标转换错误
        }
    }

    public void AddPiece(JigsawPiece piece)
    {
        pieces.Add(piece);
    }

    /// <summary>
    /// 从层级管理系统移除拼块
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UnregisterPiece(JigsawPiece piece)
    {
        // todo 重新定义
        RemovePieceThickness(piece);
    }

    /// <summary>
    /// 开始拖拽
    /// </summary>
    /// <param name="piece">拼块</param>
    public void StartDragging(JigsawPiece piece)
    {
        EnsurePieceHasThickness(piece);
        MovePieceToContainer(piece);
    }

    /// <summary>
    /// 停止拖拽
    /// </summary>
    /// <param name="piece">拼块</param>
    public void StopDragging(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed) return;
        MovePieceToContainer(piece);

        // 检查piece或组的格子下是否有其他piece或组
        CheckAndHandleUnderlyingPieces(piece);
    }

    /// <summary>
    /// 检查并处理piece或组下面的其他piece或组
    /// </summary>
    /// <param name="piece">当前拼块</param>
    private void CheckAndHandleUnderlyingPieces(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || parentPanel == null) return;

        // 获取当前piece或组占据的所有网格位置
        var occupiedGridPositions = GetOccupiedGridPositions(piece);

        // 获取操作层中的所有拼块
        var allPieces = parentPanel.GetPiecesInOperationLayer();

        // 查找在相同网格位置的其他piece或组
        var underlyingPiecesAndGroups = FindUnderlyingPiecesAndGroups(piece, occupiedGridPositions, allPieces);

        // 处理找到的底层piece或组
        foreach (var underlyingItem in underlyingPiecesAndGroups)
        {
            if (underlyingItem.IsGroup)
            {
                // 如果是组，检查组的piece数量
                if (underlyingItem.Group.Count < GetCurrentPieceOrGroupCount(piece))
                {
                    // 底层组的piece数量较少，将其置顶
                    BringGroupToTop(underlyingItem.Group);
                }
            }
            else
            {
                // 如果是单个piece，检查当前是否为组
                var currentGroup = piece.GetGroup();
                if (currentGroup != null && currentGroup.Count > 1)
                {
                    // 当前是组且数量大于1，将底层单个piece置顶
                    BringPieceToTop(underlyingItem.Piece);
                }
            }
        }
    }

    /// <summary>
    /// 获取piece或组占据的所有网格位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>网格位置列表</returns>
    private List<Vector2Int> GetOccupiedGridPositions(JigsawPiece piece)
    {
        var gridPositions = new List<Vector2Int>();
        var group = piece.GetGroup();

        if (group != null)
        {
            // 如果是组，获取组中所有piece的网格位置
            foreach (var groupPiece in group.Pieces)
            {
                var gridPos = GetPieceGridPosition(groupPiece);
                if (!gridPositions.Contains(gridPos))
                {
                    gridPositions.Add(gridPos);
                }
            }
        }
        else
        {
            // 如果是单个piece，只获取该piece的网格位置
            gridPositions.Add(GetPieceGridPosition(piece));
        }

        return gridPositions;
    }

    /// <summary>
    /// 获取拼块在操作层中的网格位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>网格位置</returns>
    private Vector2Int GetPieceGridPosition(JigsawPiece piece)
    {
        Vector2 pieceCenter = piece.LocalToGlobal(new Vector2(piece.width * 0.5f, piece.height * 0.5f));
        Vector2 operationLayerPos = parentPanel.GlobalToOperationLayerLocal(pieceCenter);
        return parentPanel.GetGridPosition(operationLayerPos);
    }

    /// <summary>
    /// 查找在相同网格位置的其他piece或组
    /// </summary>
    /// <param name="currentPiece">当前拼块</param>
    /// <param name="occupiedGridPositions">占据的网格位置</param>
    /// <param name="allPieces">所有拼块</param>
    /// <returns>底层piece或组列表</returns>
    private List<UnderlyingItem> FindUnderlyingPiecesAndGroups(JigsawPiece currentPiece, List<Vector2Int> occupiedGridPositions, List<JigsawPiece> allPieces)
    {
        var underlyingItems = new List<UnderlyingItem>();
        var processedGroups = new HashSet<JigsawGroup>();
        var currentGroup = currentPiece.GetGroup();

        foreach (var otherPiece in allPieces)
        {
            if (otherPiece == currentPiece || otherPiece.isDisposed) continue;

            // 如果是同一个组的piece，跳过
            var otherGroup = otherPiece.GetGroup();
            if (currentGroup != null && otherGroup == currentGroup) continue;

            // 检查是否在相同网格位置
            var otherGridPos = GetPieceGridPosition(otherPiece);
            if (!occupiedGridPositions.Contains(otherGridPos)) continue;

            if (otherGroup != null)
            {
                // 如果是组的一部分且该组还未处理过
                if (!processedGroups.Contains(otherGroup))
                {
                    underlyingItems.Add(new UnderlyingItem(otherGroup));
                    processedGroups.Add(otherGroup);
                }
            }
            else
            {
                // 单个piece
                underlyingItems.Add(new UnderlyingItem(otherPiece));
            }
        }

        return underlyingItems;
    }

    /// <summary>
    /// 获取当前piece或组的数量
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>piece数量</returns>
    private int GetCurrentPieceOrGroupCount(JigsawPiece piece)
    {
        var group = piece.GetGroup();
        return group?.Count ?? 1;
    }

    /// <summary>
    /// 将组置顶
    /// </summary>
    /// <param name="group">组</param>
    private void BringGroupToTop(JigsawGroup group)
    {
        if (group == null) return;

        foreach (var piece in group.Pieces)
        {
            BringPieceToTop(piece);
        }
    }

    /// <summary>
    /// 将单个piece置顶
    /// </summary>
    /// <param name="piece">拼块</param>
    private void BringPieceToTop(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed) return;

        // 调用MovePieceToContainer将piece置顶
        MovePieceToContainer(piece);
    }

    /// <summary>
    /// 将拼块移动到指定的容器
    /// </summary>
    /// <param name="piece">拼块</param>
    private void MovePieceToContainer(JigsawPiece curPiece)
    {
        if (curPiece == null) return;

        var targetContainer = parentPanel.GetPieceContainer();
        var group = curPiece.GetGroup();
        if (group != null)
        {
            var piecesToMove = group.Pieces;

            // 移动thickness
            foreach (var piece in piecesToMove)
            {
                var thickness = GetThicknessFor(piece);
                if (thickness != null && !thickness.isDisposed)
                {
                    MoveToContainer(thickness, targetContainer);
                }
            }

            // 移动拼块
            foreach (var piece in piecesToMove)
            {
                MoveToContainer(piece, targetContainer);
            }

            // 更新thickness坐标
            foreach (var piece in piecesToMove)
            {
                var thickness = GetThicknessFor(piece);
                if (thickness != null && !thickness.isDisposed)
                {
                    UpdateThicknessPosition(piece, thickness);
                }
            }
        }
        else
        {
            // 移动thickness
            var thickness = GetThicknessFor(curPiece);
            if (thickness != null && !thickness.isDisposed)
            {
                MoveToContainer(thickness, targetContainer);
            }

            // 移动拼块
            MoveToContainer(curPiece, targetContainer);

            // 更新thickness坐标
            if (thickness != null && !thickness.isDisposed)
            {
                UpdateThicknessPosition(curPiece, thickness);
            }
        }
    }
    
    /// <summary>
    /// 更新拼块位置
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UpdatePiecePosition(JigsawPiece piece)
    {
        var thickness = GetThicknessFor(piece);
        if (thickness == null) return;
        UpdateThicknessPosition(piece, thickness);
    }

    /// <summary>
    /// 将对象移动到指定容器
    /// </summary>
    /// <param name="obj">要移动的对象</param>
    /// <param name="targetContainer">目标容器</param>
    private void MoveToContainer(GObject obj, GComponent targetContainer)
    {
        if (obj == null || targetContainer == null || obj.isDisposed) return;

        try
        {
            // 记录当前的位置信息
            Vector2 globalPos = Vector2.zero;
            if (obj.parent != null)
            {
                globalPos = obj.LocalToGlobal(Vector2.zero);
            }

            if (obj.parent != null)
            {
                obj.parent.RemoveChild(obj, false);
            }

            // 添加到新容器
            targetContainer.AddChild(obj);

            // 恢复位置
            if (obj.parent != null)
            {
                Vector2 localPos = targetContainer.GlobalToLocal(globalPos);
                obj.SetXY(localPos.x, localPos.y);
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Failed to move object to container: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取拼块对应的thickness
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>thickness组件</returns>
    public GComponent GetThicknessFor(JigsawPiece piece)
    {
        if (piece == null) return null;
        
        if (pieceThicknessMap.TryGetValue(piece, out GComponent thickness))
        {
            return thickness;
        }
        
        return null;
    }
    
    /// <summary>
    /// 获取所有已注册的拼块
    /// </summary>
    /// <returns>拼块列表</returns>
    public List<JigsawPiece> GetAllRegisteredPieces()
    {
        return pieces;
    }
    
    /// <summary>
    /// 清空所有层级信息
    /// </summary>
    public void Clear()
    {
        // 清理所有thickness
        foreach (var piece in pieceThicknessMap.Keys.ToList())
        {
            RemovePieceThickness(piece);
        }

        pieces.Clear();
        pieceThicknessMap.Clear();
    }

    /// <summary>
    /// 销毁层级管理器
    /// </summary>
    public void Dispose()
    {
        Clear();
    }
}