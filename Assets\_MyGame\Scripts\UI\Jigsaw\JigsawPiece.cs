using System;
using System.Collections.Generic;
using FairyGUI;
using FairyGUI.Utils;
using UnityEngine;

public class JigsawPiece : GComponent
{
    public override void ConstructFromXML(XML xml)
    {
        base.ConstructFromXML(xml);
        DoInitialize();
    }

    private GLoader thickness;
    private GLoader picture;

    public int imageIndex;
    public int pieceIndex;
    private JigsawPanel parentPanel;
    private Vector2 globalOriginalPosition;
    private bool isDragging = false;

    // 自定义拖拽控制变量
    private bool customDragEnabled = false;
    private Vector2 touchStartPos;
    private bool isDragTesting = false;
    private const float VERTICAL_DRAG_THRESHOLD = 50f; // 垂直拖拽阈值

    // 克隆相关变量
    private JigsawPiece originalPiece;  // 如果这是克隆的piece，保存原始piece的引用
    private bool isClone = false;       // 标记是否为克隆的piece

    // 成组相关变量
    private JigsawGroup currentGroup;   // 当前所属的组
    private Vector2 groupDragOffset;    // 组拖拽时的偏移量
    private bool isGroupHighlighted = false; // 是否显示组高亮效果

    // 拖拽状态变量
    public bool wasInOperationLayerBeforeDrag = false; // 拖拽开始前是否在操作层中
    private bool isInStorage = false; // 是否在存储列表中

    private void DoInitialize()
    {
        var thicknessComp = GetChild("thicknessComp").asCom;
        thickness = thicknessComp.GetChild("thickness").asLoader;
        picture = GetChild("picture").asLoader;

        var hitTest = new RectHitTest();
        hitTest.rect = new Rect((width - 110) * 0.5f, (height - 110) * 0.5f, 110, 110);
        container.hitArea = hitTest;

        // 禁用默认拖拽功能，使用自定义拖拽控制
        draggable = false;

        // 绑定触摸事件来实现自定义拖拽控制
        onTouchBegin.Add(OnTouchBegin);
        onTouchMove.Add(OnTouchMove);
        onTouchEnd.Add(OnTouchEnd);

        // 绑定拖拽事件
        onDragStart.Add(OnDragStart);
        onDragMove.Add(OnDragMove);
        onDragEnd.Add(OnDragEnd);
    }
    internal void SetPiece(int imageIndex, int index)
    {
        this.imageIndex = imageIndex;
        this.pieceIndex = index;

        var url = $"ui://Z_Image_{imageIndex}/piece_{index}";
        thickness.url = picture.url = url;
        
        // 确保picture可见（在新的层级管理系统中picture是主要显示内容）
        picture.visible = true;
    }

    /// <summary>
    /// 克隆当前拼块，创建一个新的JigsawPiece实例
    /// </summary>
    /// <returns>克隆的JigsawPiece</returns>
    public JigsawPiece Clone()
    {
        // 创建新的JigsawPiece实例
        JigsawPiece clonedPiece = UIPackage.CreateObject("Jigsaw", "JigsawPiece") as JigsawPiece;
        clonedPiece.SetPiece(imageIndex, pieceIndex);

#if UNITY_EDITOR
            clonedPiece.gameObjectName = "JigsawPiece" + pieceIndex;
#endif

        // // 复制基本属性
        clonedPiece.pieceIndex = this.pieceIndex;
        clonedPiece.parentPanel = this.parentPanel;
        clonedPiece.isClone = true;
        clonedPiece.originalPiece = this.isClone ? this.originalPiece : this;  // 如果当前已经是克隆，则指向原始piece

        return clonedPiece;
    }

    /// <summary>
    /// 创建克隆piece并开始拖拽
    /// </summary>
    /// <param name="evt">输入事件</param>
    private void CreateCloneAndStartDrag(InputEvent evt)
    {
        if (parentPanel == null) return;

        JigsawPiece dragPiece = this.Clone();
        
        var originalPiece = dragPiece.originalPiece;
        // 设置原始piece透明度
        originalPiece.alpha = 0.3f;

        var pieceContainer = parentPanel.GetPieceContainer();
        Vector2 globalPos = this.LocalToGlobal(Vector2.zero);
        Vector2 pieceLocalPos = pieceContainer.GlobalToLocal(globalPos);
        pieceContainer.AddChild(dragPiece);
        dragPiece.SetXY(pieceLocalPos.x, pieceLocalPos.y);
        dragPiece.globalOriginalPosition = pieceLocalPos;

        // 设置克隆piece的拖拽状态
        dragPiece.isDragging = true;

        // 停止当前piece的拖拽
        this.isDragging = false;
        this.draggable = false;
        this.customDragEnabled = false;
        
        // 让克隆piece开始拖拽
        dragPiece.draggable = true;
        dragPiece.StartDrag(evt.touchId);
    }

    /// <summary>
    /// 设置父面板引用
    /// </summary>
    /// <param name="panel">父面板</param>
    public void SetParentPanel(JigsawPanel panel)
    {
        parentPanel = panel;
    }

    /// <summary>
    /// 设置是否在存储列表中
    /// </summary>
    public void SetInStorage(bool inStorage)
    {
        isInStorage = inStorage;
    }

    /// <summary>
    /// 设置thickness的可见性
    /// </summary>
    /// <param name="visible">是否可见</param>
    public void SetThicknessVisible(bool visible)
    {
        if (thickness != null)
        {
            thickness.visible = visible;
        }
    }

    /// <summary>
    /// 设置所属的组
    /// </summary>
    /// <param name="group">拼块组</param>
    public void SetGroup(JigsawGroup group)
    {
        currentGroup = group;
    }

    /// <summary>
    /// 获取当前所属的组
    /// </summary>
    /// <returns>当前组，如果没有则返回null</returns>
    public JigsawGroup GetGroup()
    {
        return currentGroup;
    }

    /// <summary>
    /// 设置组拖拽偏移量
    /// </summary>
    /// <param name="offset">偏移量</param>
    public void SetGroupDragOffset(Vector2 offset)
    {
        groupDragOffset = offset;
    }

    /// <summary>
    /// 获取组拖拽偏移量
    /// </summary>
    /// <returns>偏移量</returns>
    public Vector2 GetGroupDragOffset()
    {
        return groupDragOffset;
    }

    /// <summary>
    /// 清除组拖拽偏移量
    /// </summary>
    public void ClearGroupDragOffset()
    {
        groupDragOffset = Vector2.zero;
    }

    /// <summary>
    /// 设置组高亮效果
    /// </summary>
    /// <param name="highlighted">是否高亮</param>
    /// <param name="highlightColor">高亮颜色，如果不指定则使用默认黄色</param>
    public void SetGroupHighlight(bool highlighted, Color? highlightColor = null)
    {
        isGroupHighlighted = highlighted;
        if (highlighted)
        {
            // 使用指定的高亮颜色，如果没有指定则使用默认黄色
            Color colorToUse = highlightColor ?? Color.yellow;
            this.picture.color = colorToUse;
        }
        else
        {
            // 移除高亮效果
            this.picture.color = Color.white; // 恢复原色
        }
    }

    /// <summary>
    /// 设置锁定高亮效果（当组合达到10个且在正确位置时）
    /// </summary>
    /// <param name="locked">是否锁定</param>
    public void SetLockedHighlight(bool locked)
    {
        // TODO: 实现锁定状态的视觉效果，例如特殊边框或颜色
        // 这里可以通过修改拼块的颜色或添加特殊边框来实现
        if (locked)
        {
            // 添加锁定效果，例如设置特殊颜色或边框
            this.picture.color = Color.green; // 示例：绿色表示锁定
        }
        else
        {
            // 移除锁定效果
            this.picture.color = Color.white; // 恢复原色
        }
    }

    /// <summary>
    /// 获取拼块在原图网格中的位置
    /// </summary>
    /// <returns>网格位置</returns>
    public Vector2Int GetOriginalGridPosition()
    {
        return JigsawGroup.GetGridPosition(pieceIndex);
    }

    /// <summary>
    /// 检查与另一个拼块是否在原图中相邻
    /// </summary>
    /// <param name="otherPiece">另一个拼块</param>
    /// <returns>是否相邻</returns>
    public bool IsAdjacentTo(JigsawPiece otherPiece)
    {
        if (otherPiece == null) return false;

        Vector2Int thisPos = GetOriginalGridPosition();
        Vector2Int otherPos = otherPiece.GetOriginalGridPosition();

        return JigsawGroup.IsAdjacent(thisPos, otherPos);
    }

    /// <summary>
    /// 检查拼块是否在操作层中
    /// </summary>
    /// <returns>是否在操作层中</returns>
    private bool IsInOperationLayer()
    {
        if (parentPanel == null) return false;

        // 检查拼块的中心点是否在操作层范围内
        Vector2 centerOffset = new Vector2(width * 0.5f, height * 0.5f);
        Vector2 globalCenterPos = LocalToGlobal(centerOffset);
        Vector2 operationLayerLocalPos = parentPanel.GlobalToOperationLayerLocal(globalCenterPos);

        return parentPanel.IsPositionInOperationLayer(operationLayerLocalPos);
    }

    /// <summary>
    /// 触摸开始事件处理
    /// </summary>
    private void OnTouchBegin(EventContext context)
    {
        if (isDragging) return;  // 如果已经在拖拽中，不处理新的触摸

        var evt = context.inputEvent;
        touchStartPos = evt.position;
        isDragTesting = true;
        context.CaptureTouch();
    }

    /// <summary>
    /// 触摸移动事件处理
    /// </summary>
    private void OnTouchMove(EventContext context)
    {
        if (!isDragTesting) return;

        var evt = context.inputEvent;
        Vector2 currentPos = evt.position;
        Vector2 deltaPos = currentPos - touchStartPos;

        if (isInStorage)
        {
            // 在listStorage中，只有垂直方向超过阈值才开始拖拽
            if (Mathf.Abs(deltaPos.y) >= VERTICAL_DRAG_THRESHOLD)
            {
                isDragTesting = false;
                customDragEnabled = true;

                CreateCloneAndStartDrag(evt);
            }
        }
        else
        {
            // 不在listStorage中，使用默认的拖拽敏感度
            int sensitivity = Stage.touchScreen ? UIConfig.touchDragSensitivity : UIConfig.clickDragSensitivity;
            if (Mathf.Abs(deltaPos.x) >= sensitivity || Mathf.Abs(deltaPos.y) >= sensitivity)
            {
                // 检查组合是否可以拖动
                if (currentGroup != null && !currentGroup.CanDrag())
                {
                    // 如果组合不能拖动，停止拖拽测试
                    isDragTesting = false;
                    return;
                }

                isDragTesting = false;
                customDragEnabled = true;
                draggable = true; // 启用拖拽
                StartDrag(evt.touchId);
            }
        }
    }

    /// <summary>
    /// 触摸结束事件处理
    /// </summary>
    private void OnTouchEnd(EventContext context)
    {
        isDragTesting = false;
        if (customDragEnabled)
        {
            customDragEnabled = false;
            draggable = false; // 禁用拖拽
        }
    }

    /// <summary>
    /// 拖拽开始事件处理
    /// </summary>
    private void OnDragStart(EventContext context)
    {
        if (currentGroup != null && !currentGroup.CanDrag())
        {
            // 如果组合不能拖动，取消拖拽
            context.PreventDefault();
            return;
        }

        if (parentPanel == null) return;

        isDragging = true;

        // 注意：从listStorage的拖拽现在通过OnTouchMove中的CreateCloneAndStartDrag处理
        // 这里只处理非storage的拖拽或者已经是克隆piece的拖拽

        // 将当前位置转换为全局坐标
        Vector2 globalPos = LocalToGlobal(Vector2.zero);

        // 记录拖拽开始前是否在操作层中
        wasInOperationLayerBeforeDrag = IsInOperationLayer();

        // 获取层级管理系统的渲染容器
        var operationLayer = parentPanel.GetOperationLayer();
        var pieceContainer = parentPanel.GetPieceContainer();

        // 如果拼块不在渲染容器中，需要移动到渲染容器中以便层级管理
        if (this.parent != pieceContainer)
        {
            this.RemoveFromParent();
            pieceContainer.AddChild(this);
            Vector2 newLocalPos = pieceContainer.GlobalToLocal(globalPos);
            this.SetXY(newLocalPos.x, newLocalPos.y);
        }

        // 使用新的层级管理系统
        operationLayer?.OnPieceDragStart(this);

        // 确保拼块可见且不透明
        this.visible = true;
        this.alpha = 1.0f;
        if (picture != null)
        {
            picture.visible = true;
            picture.alpha = 1.0f;
        }

        // 如果拼块属于某个组，需要先更新组中所有拼块的原始位置信息
        if (currentGroup != null)
        {
            // 更新组中所有拼块的原始位置信息
            foreach (var piece in currentGroup.Pieces)
            {
                piece.wasInOperationLayerBeforeDrag = piece.IsInOperationLayer();

                // 如果组中的拼块不在渲染容器中，也需要移动
                if (piece.parent != pieceContainer)
                {
                    Vector2 pieceGlobalPos = piece.LocalToGlobal(Vector2.zero);
                    piece.RemoveFromParent();
                    pieceContainer.AddChild(piece);
                    Vector2 pieceNewLocalPos = pieceContainer.GlobalToLocal(pieceGlobalPos);
                    piece.SetXY(pieceNewLocalPos.x, pieceNewLocalPos.y);
                }

                // 使用新的层级管理系统
                operationLayer?.OnPieceDragStart(piece);
            }
        }

        // 从原始父容器中移除
        // originalParent.RemoveChild(this, false);

        // 新系统中不需要移动到DragLayer，通过层级索引控制显示顺序
        // 只记录原始位置用于返回
        globalOriginalPosition = xy;

        // 如果拼块属于某个组，开始组拖拽并更新组中其他拼块的globalOriginalPosition
        if (currentGroup != null)
        {
            currentGroup.StartGroupDrag(this, xy);

            // 新系统中组中其他拼块也不需要移动容器，只记录原始位置
            foreach (var piece in currentGroup.Pieces)
            {
                if (piece != this)
                {
                    piece.globalOriginalPosition = piece.xy;
                }
            }

            // 新系统中层级自动管理
        }
        else
        {
            // 新系统中单个拼块的层级也自动管理
        }
        
    }

    /// <summary>
    /// 拖拽移动事件处理
    /// </summary>
    private void OnDragMove(EventContext context)
    {
        if (!isDragging) return;

        // 使用新的层级管理系统
        if (parentPanel != null)
        {
            var operationLayer = parentPanel.GetOperationLayer();
            operationLayer?.OnPiecePositionChanged(this);
        }

        // 如果拼块属于某个组，更新组拖拽
        if (currentGroup != null)
        {
            currentGroup.UpdateGroupDrag(this, xy);
        }

        // 在拖拽过程中可以添加一些视觉反馈
        // 比如高亮显示可能的放置位置等
    }

    /// <summary>
    /// 拖拽结束事件处理
    /// </summary>
    private void OnDragEnd(EventContext context)
    {
        // 检查是否拖拽到操作层区域
        HandleDropToOperationLayer();
    }

    private void OnPieceDragEnd()
    {
        if (!isDragging) return;

        isDragging = false;

        // 使用新的层级管理系统
        if (parentPanel != null)
        {
            var operationLayer = parentPanel.GetOperationLayer();
            operationLayer?.OnPieceDragEnd(this);
        }

        // 如果拼块属于某个组，结束组拖拽
        if (currentGroup != null)
        {
            currentGroup.EndGroupDrag();
        }

        // 重置自定义拖拽状态
        if (customDragEnabled)
        {
            customDragEnabled = false;
            draggable = false; // 禁用拖拽
        }
    }

    /// <summary>
    /// 处理拖拽到操作层的逻辑
    /// </summary>
    private void HandleDropToOperationLayer()
    {
        // 将拼块中心点位置转换为操作层的本地坐标
        // 由于拼块的pivot是(0.5, 0.5)，需要获取中心点位置
        Vector2 centerOffset = new Vector2(width * 0.5f, height * 0.5f);
        Vector2 globalCenterPos = LocalToGlobal(centerOffset);
        Vector2 operationLayerLocalPos = parentPanel.GlobalToOperationLayerLocal(globalCenterPos);

        // 检查是否在操作层范围内
        if (parentPanel.IsPositionInOperationLayer(operationLayerLocalPos))
        {
            // 获取网格坐标
            Vector2Int gridPos = parentPanel.GetGridPosition(operationLayerLocalPos);
            Vector2 targetPos = parentPanel.GetLocalPosition(gridPos);

            // 将目标位置转换回全局坐标，再转换为当前父容器的本地坐标
            Vector2 globalTargetPos = parentPanel.OperationLayerLocalToGlobal(targetPos);
            // 由于目标位置是网格中心点，需要减去拼块中心偏移，得到拼块左上角应该放置的位置
            Vector2 localTargetPos = parent.GlobalToLocal(globalTargetPos) - centerOffset;

            // 如果拼块属于组，需要同时移动组中的其他拼块
            if (currentGroup != null)
            {
                // 计算移动偏移量
                Vector2 moveOffset = localTargetPos - xy;

                // 同时移动组中的所有拼块
                foreach (var piece in currentGroup.Pieces)
                {
                    Vector2 pieceTargetPos = piece.xy + moveOffset;
                    var tween = piece.TweenMove(pieceTargetPos, 0.3f);

                    // 在缓动过程中实时更新thickness位置
                    tween.OnUpdate(() =>
                    {
                        if (parentPanel != null)
                        {
                            var operationLayer = parentPanel.GetOperationLayer();
                            operationLayer?.OnPiecePositionChanged(piece);
                        }
                    });

                    tween.OnComplete(() =>
                    {
                        piece.OnPieceDragEnd();
                        // var operationLayer = parentPanel.GetOperationLayer();

                        // // 使用新的层级管理系统
                        // operationLayer?.AddPieceToLayer(piece);
                    });
                }

                // 检测并处理成组逻辑（只需要对主拖拽拼块执行一次）,todo，缓动完再执行
                parentPanel.CheckAndCreateGroups(this);
            }
            else
            {
                // 单个拼块的移动
                var tween = TweenMove(localTargetPos, 0.3f);

                // 在缓动过程中实时更新thickness位置
                tween.OnUpdate(() =>
                {
                    if (parentPanel != null)
                    {
                        var operationLayer = parentPanel.GetOperationLayer();
                        operationLayer?.OnPiecePositionChanged(this);
                    }
                });

                tween.OnComplete(() =>
                {
                    var operationLayer = parentPanel.GetOperationLayer();
                    var layerManager = operationLayer?.GetLayerManager();
                    layerManager?.AddPiece(this);

                    // 使用新的层级管理系统
                    // operationLayer?.AddPieceToLayer(this);

                    // 检测并处理成组逻辑
                    parentPanel.CheckAndCreateGroups(this);
                    
                    OnPieceDragEnd();
                });
            }

            // 如果是克隆的piece成功放置，删除原始piece
            if (isClone && originalPiece != null)
            {
                isClone = false;
                isInStorage = false;
                originalPiece.Dispose();
                originalPiece = null;
            }
        }
        else
        {
            if (originalPiece != null)
            {
                originalPiece.alpha = 1.0f;
            }
            ReturnToOriginalPosition();
        }
    }

    /// <summary>
    /// 回到原始位置
    /// </summary>
    private void ReturnToOriginalPosition()
    {
        if (parentPanel == null) return;
        var operationLayer = parentPanel.GetOperationLayer();
        if (currentGroup != null)
        {
            //处理组的情况
            foreach (var piece in currentGroup.Pieces)
            {
                var tween = piece.TweenMove(piece.globalOriginalPosition, 0.3f);

                // 在缓动过程中实时更新thickness位置
                tween.OnUpdate(() =>
                {
                    operationLayer?.OnPiecePositionChanged(piece);
                })
                .OnComplete(()=>
                {
                    OnPieceDragEnd();
                });
            }
        }
        else
        {
            // 单个拼块的返回
            var tween = TweenMove(globalOriginalPosition, 0.3f);
            tween.OnUpdate(() =>
            {
                operationLayer?.OnPiecePositionChanged(this);
            })
            .OnComplete(() =>
            {
                if (this.isDisposed) return;
                if (isClone)
                {
                    // 如果是克隆，直接销毁(从贮存区拖出的piece)
                    var layerManager = operationLayer?.GetLayerManager();
                    layerManager?.UnregisterPiece(this);

                    this.Dispose();
                }
                else
                {
                    OnPieceDragEnd();
                }
            });
        }
    }
}

